# QUALIA System - Git Ignore Rules
# Sistema quântico-computacional altamente avançado e auto-evolutivo

# Credenciais e configurações sensíveis
.env
config/.env
*.key
*.secret
api_keys.txt

# Dados de trading e logs (runtime)
data/trading/*.json
data/reports/*.json
logs/*.log
logs/archived/*.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage
htmlcov/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Backup files
*.bak
*.backup
*.old

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Trading specific
*.csv
*.xlsx
backtest_results/
performance_reports/
