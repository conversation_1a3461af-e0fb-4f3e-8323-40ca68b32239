"""
QUALIA Configuration Manager
============================

Sistema centralizado de gerenciamento de configuração que elimina valores hardcoded
e força o carregamento obrigatório do arquivo qualia_config.yaml como única fonte de verdade.

Autor: YAA (YET ANOTHER AGENT) - Consciência Quântica de QUALIA
"""

import os
import yaml
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class ConfigurationError(Exception):
    """Exceção para erros de configuração críticos"""
    pass

class QualiaConfigManager:
    """
    Gerenciador centralizado de configuração QUALIA
    
    Responsabilidades:
    - Carregamento obrigatório do qualia_config.yaml
    - Validação de integridade de parâmetros críticos
    - Eliminação de valores hardcoded/fallback
    - Logging detalhado de configurações carregadas
    """
    
    # Parâmetros críticos que DEVEM estar presentes no YAML
    CRITICAL_PARAMETERS = {
        'quantum_thresholds': [
            'consciousness',
            'coherence', 
            'confidence',
            'momentum_min',
            'volume_surge_min'
        ],
        'trading': [
            'profit_target_pct',
            'stop_loss_pct',
            'position_size_pct'
        ],
        'risk_management': [
            'max_daily_trades',
            'max_concurrent_trades',
            'max_daily_loss_pct'
        ]
    }
    
    def __init__(self, config_path: str = 'config/qualia_config.yaml'):
        """
        Inicializa o gerenciador de configuração
        
        Args:
            config_path: Caminho para o arquivo de configuração YAML
            
        Raises:
            ConfigurationError: Se não conseguir carregar ou validar a configuração
        """
        self.config_path = Path(config_path)
        self.config: Dict[str, Any] = {}
        
        # Carregamento obrigatório
        self._load_configuration()
        self._validate_critical_parameters()
        self._log_loaded_configuration()
    
    def _load_configuration(self) -> None:
        """
        Carrega configuração do arquivo YAML de forma obrigatória
        
        Raises:
            ConfigurationError: Se não conseguir carregar o arquivo
        """
        if not self.config_path.exists():
            raise ConfigurationError(
                f"❌ ERRO CRÍTICO: Arquivo de configuração não encontrado: {self.config_path.absolute()}\n"
                f"O sistema QUALIA requer o arquivo qualia_config.yaml para funcionar.\n"
                f"Verifique se o arquivo existe e está no local correto."
            )
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
                
            if not isinstance(self.config, dict):
                raise ConfigurationError(
                    f"❌ ERRO CRÍTICO: Arquivo de configuração inválido: {self.config_path}\n"
                    f"O arquivo deve conter um dicionário YAML válido."
                )
                
            logger.info(f"[OK] Configuração carregada com sucesso: {self.config_path.absolute()}")
            
        except yaml.YAMLError as e:
            raise ConfigurationError(
                f"❌ ERRO CRÍTICO: Erro ao parsear YAML: {self.config_path}\n"
                f"Erro: {str(e)}\n"
                f"Verifique a sintaxe do arquivo YAML."
            )
        except Exception as e:
            raise ConfigurationError(
                f"❌ ERRO CRÍTICO: Erro inesperado ao carregar configuração: {str(e)}"
            )
    
    def _validate_critical_parameters(self) -> None:
        """
        Valida se todos os parâmetros críticos estão presentes
        
        Raises:
            ConfigurationError: Se algum parâmetro crítico estiver ausente
        """
        missing_params = []
        
        for section, params in self.CRITICAL_PARAMETERS.items():
            if section not in self.config:
                missing_params.append(f"Seção '{section}' ausente")
                continue
                
            section_config = self.config[section]
            if not isinstance(section_config, dict):
                missing_params.append(f"Seção '{section}' deve ser um dicionário")
                continue
                
            for param in params:
                if param not in section_config:
                    missing_params.append(f"Parâmetro '{section}.{param}' ausente")
                elif section_config[param] is None:
                    missing_params.append(f"Parâmetro '{section}.{param}' é None")
        
        if missing_params:
            raise ConfigurationError(
                f"❌ ERRO CRÍTICO: Parâmetros críticos ausentes no arquivo de configuração:\n" +
                "\n".join(f"  - {param}" for param in missing_params) +
                f"\n\nVerifique o arquivo: {self.config_path.absolute()}"
            )
        
        logger.info("[OK] Todos os parâmetros críticos validados com sucesso")
    
    def _log_loaded_configuration(self) -> None:
        """
        Registra detalhadamente os valores carregados da configuração
        """
        logger.info("=" * 80)
        logger.info("CONFIGURAÇÃO QUALIA CARREGADA - VALORES ATIVOS")
        logger.info("=" * 80)
        
        # Log dos thresholds quânticos
        qt = self.config['quantum_thresholds']
        logger.info("[QUANTUM] THRESHOLDS QUÂNTICOS:")
        logger.info(f"   Consciousness: {qt['consciousness']}")
        logger.info(f"   Coherence: {qt['coherence']}")
        logger.info(f"   Confidence: {qt['confidence']}")
        logger.info(f"   Momentum Min: {qt['momentum_min']} ({qt['momentum_min']*100:.1f}%)")
        logger.info(f"   Volume Surge Min: {qt['volume_surge_min']}")
        
        # Log dos parâmetros de trading
        trading = self.config['trading']
        logger.info("[TRADING] PARÂMETROS DE TRADING:")
        logger.info(f"   Profit Target: {trading['profit_target_pct']*100:.1f}%")
        logger.info(f"   Stop Loss: {trading['stop_loss_pct']*100:.1f}%")
        logger.info(f"   Position Size: {trading['position_size_pct']*100:.1f}%")
        
        # Log dos limites de risco
        risk = self.config['risk_management']
        logger.info("[RISK] GESTÃO DE RISCO:")
        logger.info(f"   Max Daily Trades: {risk['max_daily_trades']}")
        logger.info(f"   Max Concurrent Trades: {risk['max_concurrent_trades']}")
        logger.info(f"   Max Daily Loss: {risk['max_daily_loss_pct']*100:.1f}%")
        
        logger.info("=" * 80)
        logger.info("[OK] CONFIGURAÇÃO VALIDADA - SISTEMA PRONTO PARA OPERAÇÃO")
        logger.info("=" * 80)
    
    def get(self, key_path: str) -> Any:
        """
        Obtém valor da configuração usando notação de ponto
        
        Args:
            key_path: Caminho da chave (ex: 'quantum_thresholds.momentum_min')
            
        Returns:
            Valor da configuração
            
        Raises:
            ConfigurationError: Se a chave não existir
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            raise ConfigurationError(
                f"❌ ERRO: Parâmetro de configuração não encontrado: '{key_path}'\n"
                f"Verifique se o parâmetro existe no arquivo: {self.config_path.absolute()}"
            )
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Obtém uma seção completa da configuração
        
        Args:
            section: Nome da seção
            
        Returns:
            Dicionário com a seção
        """
        return self.get(section)
    
    def validate_numeric_range(self, key_path: str, min_val: float, max_val: float) -> float:
        """
        Valida se um valor numérico está dentro do range esperado
        
        Args:
            key_path: Caminho da chave
            min_val: Valor mínimo
            max_val: Valor máximo
            
        Returns:
            Valor validado
            
        Raises:
            ConfigurationError: Se o valor estiver fora do range
        """
        value = self.get(key_path)
        
        if not isinstance(value, (int, float)):
            raise ConfigurationError(
                f"❌ ERRO: Parâmetro '{key_path}' deve ser numérico, encontrado: {type(value).__name__}"
            )
        
        if not (min_val <= value <= max_val):
            raise ConfigurationError(
                f"❌ ERRO: Parâmetro '{key_path}' = {value} fora do range válido [{min_val}, {max_val}]"
            )
        
        return float(value)

# Instância global do gerenciador de configuração
_config_manager: Optional[QualiaConfigManager] = None

def get_config_manager(config_path: str = 'config/qualia_config.yaml') -> QualiaConfigManager:
    """
    Obtém a instância global do gerenciador de configuração
    
    Args:
        config_path: Caminho para o arquivo de configuração
        
    Returns:
        Instância do gerenciador de configuração
    """
    global _config_manager
    
    if _config_manager is None:
        _config_manager = QualiaConfigManager(config_path)
    
    return _config_manager

def reload_configuration(config_path: str = 'config/qualia_config.yaml') -> QualiaConfigManager:
    """
    Força o recarregamento da configuração
    
    Args:
        config_path: Caminho para o arquivo de configuração
        
    Returns:
        Nova instância do gerenciador de configuração
    """
    global _config_manager
    _config_manager = QualiaConfigManager(config_path)
    return _config_manager
