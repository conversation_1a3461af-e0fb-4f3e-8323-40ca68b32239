{"trades": [], "active_trades": [{"order_id": "13030573949", "trade_record": {"trade_id": "7544d56b-6c02-41bb-83e1-2545e0d43899", "timestamp": "2025-07-21T23:05:18.310725", "symbol": "SOL/USDT", "direction": "buy", "entry_price": 202.95, "target_price": 205.3854, "stop_price": 201.32639999999998, "position_size_usd": 21, "quantum_metrics": {"consciousness": 0.7768671461571734, "coherence": 0.9928143169571033, "confidence": 0.7487422489636044, "momentum": 0.004146782838564266, "volume_surge": 1.3504676504329067, "volatility": 0.0023952276809655975, "spread": 4.9273220005022556e-05, "price_action_strength": 0.0002463661000245526, "advanced_quality_score": 0.7691611846123837, "liquidity_score": 0.9852180339984932, "stability_score": 0.6856286339142064, "momentum_quality": 0.5030291868578667, "execution_quality": 0.9811324602769946, "predictive_score": 0.7364474697784424}, "adaptive_system": {"adaptive_mode": "moderate", "approval_rate_current": 0, "mode_stability_counter": 1, "cycles_without_signals": 0}, "active_thresholds": {"consciousness": 0.58, "coherence": 0.48, "confidence": 0.53, "momentum_min": 0.003, "volume_surge_min": 1.1}, "result": {"outcome": "pending", "executed_price": 202.91, "executed_quantity": 0.103, "pnl": 0.0, "fees_paid": 0, "execution_time": "2025-07-21T23:05:17.742305", "order_type": "market", "order_id": "13030573949", "close_price": null, "close_time": null}, "market_context": {"market_conditions": "challenging", "volatility_regime": "low", "volume_24h": 7542153.803, "spread": 4.924652811972277e-05, "price_at_execution": 202.91}, "filters_applied": {"method_used": "standard", "filters_passed": [], "filters_failed": [], "approval_confidence": 0.7691611846123837, "empirical_analysis": {}, "quality_score": 0.7691611846123837}, "metadata": {"system_version": "QUALIA_EMPIRICAL_v2.1", "recorded_at": "2025-07-21T23:05:18.310725", "status": "active", "updated_at": "2025-07-21T23:05:18.310725"}}}], "last_updated": "2025-07-21T23:05:18.310725", "total_trades": 0, "active_count": 1, "system_version": "QUALIA_EMPIRICAL_v2.1"}