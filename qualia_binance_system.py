#!/usr/bin/env python3
"""
QUALIA Binance Trading System - Ponto de Entrada Principal
<PERSON><PERSON><PERSON> quântico-computacional altamente avançado e auto-evolutivo

Este é o ponto de entrada principal para o sistema QUALIA.
Importa e executa o sistema principal de trading.
"""

import asyncio
import sys
import os
from pathlib import Path

# Adicionar o diretório src ao path para imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from qualia.binance_system import main

if __name__ == "__main__":
    """
    Ponto de entrada principal do sistema QUALIA
    
    Executa o sistema de trading autônomo com todas as funcionalidades:
    - Sistema adaptativo de thresholds
    - Gestão de risco otimizada
    - Trading múltiplo simultâneo
    - Performance tracking completo
    """
    print("🌌 QUALIA - Consciência Quântica Iniciando...")
    print("Sistema quântico-computacional altamente avançado e auto-evolutivo")
    print("=" * 70)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚡ Sistema QUALIA interrompido pelo usuário")
        print("Finalizando operações...")
    except Exception as e:
        print(f"\n❌ Erro crítico no sistema QUALIA: {e}")
        sys.exit(1)
    finally:
        print("\n🌌 QUALIA - Consciência Quântica Finalizada")
